SIGNIFICATION PRÉCISE :
TauxT calcule l'entropie théorique moyenne des sous-séquences de longueur 3 dans la séquence [1:n], utilisant les probabilités INDEX5 sous hypothèse d'indépendance.

C'est une mesure de la complexité informationnelle moyenne par "motif de longueur 3" dans la séquence.

RELATION AVEC CondT :
La documentation prétend que "TauxT_n = CondT_n", mais c'est FAUX car :

TauxT divise par (n-2)
CondT divise par n
EN RÉSUMÉ :
TauxT calcule l'entropie moyenne par sous-séquence de longueur 3 dans [1:n], en divisant la somme des entropies de toutes les sous-séquences (BlockT) par le nombre de sous-séquences (n-2).


COMPRÉHENSION EXPERTE DE TauxT.jl
Après avoir lu complètement le code ligne par ligne, voici exactement ce que calcule la métrique TauxT :

CE QUE FAIT TauxT EN LANGAGE NATUREL :
Appelle la fonction BlockT pour calculer l'entropie de bloc : block_entropy = calculer_formule10B_block_cumulative_theo(formulas, sequence, n)
Calcule le nombre de sous-séquences de longueur 3 : nb_subsequences = n - 2
Divise BlockT par le nombre de sous-séquences : return block_entropy / T(nb_subsequences)
FORMULE EXACTE :
TauxT_n = BlockT_n / (n - 2)

CE QUE CELA SIGNIFIE CONCRÈTEMENT :
Puisque BlockT calcule la somme des entropies de toutes les sous-séquences de longueur 3, TauxT calcule la moyenne de ces entropies.

Exemple :
Si la séquence [1:5] est ["A", "B", "C", "D", "E"] :

BlockT calcule : H(A,B,C) + H(B,C,D) + H(C,D,E) (somme de 3 termes)
TauxT calcule : [H(A,B,C) + H(B,C,D) + H(C,D,E)] / 3 (moyenne de 3 termes)
INCOHÉRENCE DANS LA DOCUMENTATION :
Documentation :
TauxT_n = (1/n) × BlockT_n
"TauxT utilise BlockT puis divise par n"
Code réel :
TauxT_n = BlockT_n / (n - 2)
Divise par le nombre de sous-séquences, pas par n
SIGNIFICATION PRÉCISE :
TauxT calcule l'entropie théorique moyenne des sous-séquences de longueur 3 dans la séquence [1:n], utilisant les probabilités INDEX5 sous hypothèse d'indépendance.

C'est une mesure de la complexité informationnelle moyenne par "motif de longueur 3" dans la séquence.

