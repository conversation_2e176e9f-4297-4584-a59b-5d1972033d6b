SIGNIFICATION PRÉCISE :
BlockT calcule la somme des entropies de Shannon de toutes les sous-séquences de longueur 3 dans [1:n], où chaque sous-séquence est évaluée selon les probabilités théoriques INDEX5 sous hypothèse d'indépendance.

DIFFÉRENCE AVEC LA DOCUMENTATION :
Documentation : "entropie jointe selon la règle de chaîne"
Code réel : Somme d'entropies de sous-séquences de longueur 3 avec hypothèse d'indépendance
EN RÉSUMÉ :
BlockT calcule la somme des entropies théoriques de toutes les sous-séquences consécutives de longueur 3 dans la séquence [1:n], utilisant les probabilités INDEX5 marginales.


COMPRÉHENSION EXPERTE DE BlockT.jl
Après avoir lu complètement le code ligne par ligne, voici exactement ce que calcule la métrique BlockT :

CE QUE FAIT BlockT EN LANGAGE NATUREL :
Prend une séquence de valeurs INDEX5 et un nombre n
Extrait les n premières valeurs : sequence_n = sequence[1:n]
Parcourt toutes les sous-séquences de longueur 3 dans cette séquence
Pour chaque sous-séquence de longueur 3 :
Calcule sa probabilité théorique jointe : p_joint = p_theo(val1) × p_theo(val2) × p_theo(val3)
Applique la formule de Shannon : -p_joint × log₂(p_joint)
Additionne tous ces termes
EXEMPLE CONCRET :
Si la séquence [1:5] est ["A", "B", "C", "D", "E"] :

Sous-séquences de longueur 3 :
Position 1 : ["A", "B", "C"]
Position 2 : ["B", "C", "D"]
Position 3 : ["C", "D", "E"]
Calculs :
Pour ["A", "B", "C"] :
p_joint = p_theo(A) × p_theo(B) × p_theo(C)
Terme = -p_joint × log₂(p_joint)
Pour ["B", "C", "D"] :
p_joint = p_theo(B) × p_theo(C) × p_theo(D)
Terme = -p_joint × log₂(p_joint)
Pour ["C", "D", "E"] :
p_joint = p_theo(C) × p_theo(D) × p_theo(E)
Terme = -p_joint × log₂(p_joint)
Résultat : Somme des trois termes
NOMBRE DE SOUS-SÉQUENCES :
Pour une séquence de longueur n, il y a (n-2) sous-séquences de longueur 3 :

length(sequence_n) - 3 + 1 = n - 3 + 1 = n - 2
CALCUL DES PROBABILITÉS JOINTES :
La fonction calculer_probabilite_jointe_theo utilise l'hypothèse d'indépendance :

p_theo(séquence) = p_theo(val1) × p_theo(val2) × p_theo(val3)