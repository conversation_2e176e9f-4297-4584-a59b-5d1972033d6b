INDEX1 : 0, 1 
INDEX2 : A, B, C
INDEX3 : <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TIE
INDEX5 : 

0_A_BANKER	
0_B_BANKER	
0_C_BANKER	
0_A_PLAYER	
0_B_PLAYER	
0_C_PLAYER
0_A_TIE		
0_B_TIE		
0_C_TIE

1_A_BANKER
1_B_BANKER
1_C_BANKER
1_A_PLAYER
1_B_PLAYER
1_C_PLAYER
1_A_TIE
1_B_TIE
1_C_TIE
		
0_A_BANKER 0_B_BANKER 0_C_BANKER 0_A_PLAYER 0_B_PLAYER 0_C_PLAYER 0_A_TIE 0_B_TIE 0_C_TIE 1_A_BANKER 1_B_BANKER 1_C_BANKER 1_A_PLAYER 1_B_PLAYER 1_C_PLAYER 1_A_TIE 1_B_TIE 1_C_TIE

- Nous savons que : 

Si INDEX1 = 0 à la main n avec INDEX2 = C à la main n, alors à la main n+1 : INDEX1 sera égal à 1
Si INDEX1 = 1 à la main n avec INDEX2 = C à la main n, alors à la main n+1 : INDEX1 sera égal à 0

Si INDEX1 = 0 à la main n avec INDEX2 = A à la main n, alors à la main n+1 : INDEX1 sera égal à 0
Si INDEX1 = 1 à la main n avec INDEX2 = A à la main n, alors à la main n+1 : INDEX1 sera égal à 1

Si INDEX1 = 0 à la main n avec INDEX2 = B à la main n, alors à la main n+1 : INDEX1 sera égal à 0
Si INDEX1 = 1 à la main n avec INDEX2 = B à la main n, alors à la main n+1 : INDEX1 sera égal à 1
